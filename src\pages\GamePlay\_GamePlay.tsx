import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { ArrowLeft, Pause, Play, RotateCcw, Trophy, Users, Timer, Zap, Star, Crown, Target, Award, Coins, TrendingUp, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { useWallet } from '@/contexts/WalletContext';
import styles from './GamePlay.module.css';

const GamePlay = () => {
  const { gameId } = useParams();
  const { isConnected } = useWallet();
  const [isPlaying, setIsPlaying] = useState(false);
  const [score, setScore] = useState(0);
  const [timeLeft, setTimeLeft] = useState(120); // 2 minutes
  const [gameProgress, setGameProgress] = useState(0);
  const [combo, setCombo] = useState(0);
  const [powerUps, setPowerUps] = useState(0);

  // Mock game data
  const game = {
    id: gameId,
    title: 'Crypto Runner Elite',
    description: 'Navigate neon-lit cyberpunk cities collecting rare digital artifacts!',
    blockchain: 'BSC',
    maxRewards: 200,
    difficulty: 'Easy',
    category: 'Endless Runner',
  };

  const playerStats = {
    bestScore: 15420,
    gamesPlayed: 127,
    totalEarned: 2340,
    winRate: 78,
    averageScore: 8560,
    level: 42,
  };

  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (isPlaying && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            setIsPlaying(false);
            return 0;
          }
          return prev - 1;
        });
        
        // Simulate enhanced gameplay
        const baseScore = Math.floor(Math.random() * 15) + 5;
        const comboBonus = combo * 2;
        const powerUpBonus = powerUps * 10;
        const totalScore = baseScore + comboBonus + powerUpBonus;
        
        setScore(prev => prev + totalScore);
        setGameProgress(prev => Math.min(prev + 0.83, 100)); // 100% in 120 seconds
        
        // Random combo and power-up generation
        if (Math.random() > 0.7) setCombo(prev => Math.min(prev + 1, 10));
        if (Math.random() > 0.85) setPowerUps(prev => prev + 1);
        if (Math.random() > 0.8) setCombo(0); // Reset combo occasionally
      }, 1000);
    }

    return () => clearInterval(interval);
  }, [isPlaying, timeLeft, combo, powerUps]);

  const handleStartGame = () => {
    if (!isConnected) return;
    setIsPlaying(true);
  };

  const handlePauseGame = () => {
    setIsPlaying(false);
  };

  const handleRestart = () => {
    setIsPlaying(false);
    setScore(0);
    setTimeLeft(120);
    setGameProgress(0);
    setCombo(0);
    setPowerUps(0);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const estimatedReward = Math.floor((score / 1000) * game.maxRewards);
  const rankPosition = score > playerStats.bestScore ? 1 : Math.floor(Math.random() * 5) + 2;

  if (!isConnected) {
    return (
      <div className="min-h-screen relative overflow-hidden">
        {/* Background */}
        <div className="fixed inset-0 bg-gradient-to-br from-deep-space via-card-dark/50 to-surface-dark/30 pointer-events-none"></div>
        <div className="fixed inset-0 digital-grid opacity-10 pointer-events-none"></div>
        
        <div className="relative z-10 flex items-center justify-center min-h-screen p-4">
          <div className="bg-gradient-to-br from-card-dark/90 to-surface-dark/80 backdrop-blur-xl border border-border-light/30 rounded-2xl p-12 text-center max-w-md mx-auto hover:border-accent-cyan/30 transition-all duration-300">
            <div className="mb-8">
              <div className="w-24 h-24 bg-gradient-to-br from-accent-cyan/20 to-xp-purple/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <Shield className="h-12 w-12 text-accent-cyan" />
              </div>
              <div className="text-6xl mb-4">🎮</div>
            </div>
            <h2 className="text-3xl font-bold mb-4 bg-gradient-to-r from-accent-cyan to-xp-purple bg-clip-text text-transparent">
              Connect Your Wallet
            </h2>
            <p className="text-secondary-text mb-6 leading-relaxed">
              You need to connect your wallet to play games and earn epic rewards in the gaming multiverse.
            </p>
            <Button asChild className="bg-gradient-to-r from-gaming-gold to-legendary-orange text-deep-space font-bold px-8 py-3 hover:scale-105 transition-all duration-300">
              <Link to="/games">Back to Gaming Hub</Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Enhanced Background */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-br from-deep-space via-card-dark/50 to-surface-dark/30"></div>
        <div className="absolute inset-0 digital-grid opacity-5"></div>
        
        {/* Gaming particles */}
        {[...Array(15)].map((_, i) => (
          <div
            key={i}
            className="absolute pointer-events-none"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animation: `float-advanced ${6 + Math.random() * 4}s ease-in-out infinite`,
              animationDelay: `${Math.random() * 3}s`,
            }}
          >
            <div 
              className="w-16 h-16 opacity-10"
              style={{
                background: `radial-gradient(circle, ${
                  ['#06B6D4', '#8B5CF6', '#F59E0B'][i % 3]
                }30, transparent 70%)`,
                borderRadius: '50%',
              }}
            />
          </div>
        ))}
      </div>

      {/* Enhanced Game Header */}
      <div className="relative z-10 bg-gradient-to-r from-card-dark/95 to-surface-dark/90 backdrop-blur-xl border-b border-border-light/30 pt-20 pb-4 px-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <Button asChild variant="ghost" size="sm" className="hover:bg-accent-cyan/10 hover:text-accent-cyan transition-all duration-300">
              <Link to="/games">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Hub
              </Link>
            </Button>
            <div className="flex items-center space-x-4">
              <div className="text-6xl">{game.id === '1' ? '🏃‍♂️' : game.id === '2' ? '🧩' : '🏰'}</div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-accent-cyan to-xp-purple bg-clip-text text-transparent">
                  {game.title}
                </h1>
                <p className="text-secondary-text">{game.description}</p>
                <div className="flex items-center gap-3 mt-2">
                  <span className="px-2 py-1 text-xs font-bold bg-binance-gold/20 text-binance-gold rounded-full">
                    {game.blockchain}
                  </span>
                  <span className="px-2 py-1 text-xs font-medium bg-success-green/20 text-success-green rounded-full">
                    {game.difficulty}
                  </span>
                  <span className="px-2 py-1 text-xs font-medium bg-xp-purple/20 text-xp-purple rounded-full">
                    {game.category}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Stats Display */}
          <div className="flex items-center space-x-6">
            <div className="text-center bg-surface-dark/60 rounded-xl p-3 min-w-[80px]">
              <div className="flex items-center justify-center mb-1">
                <Target className="w-4 h-4 text-accent-cyan mr-1" />
              </div>
              <p className="text-xs text-secondary-text">Score</p>
              <p className="text-xl font-bold text-accent-cyan">{score.toLocaleString()}</p>
            </div>
            <div className="text-center bg-surface-dark/60 rounded-xl p-3 min-w-[80px]">
              <div className="flex items-center justify-center mb-1">
                <Timer className="w-4 h-4 text-legendary-orange mr-1" />
              </div>
              <p className="text-xs text-secondary-text">Time</p>
              <p className="text-xl font-bold text-legendary-orange">{formatTime(timeLeft)}</p>
            </div>
            <div className="text-center bg-surface-dark/60 rounded-xl p-3 min-w-[80px]">
              <div className="flex items-center justify-center mb-1">
                <Coins className="w-4 h-4 text-gaming-gold mr-1" />
              </div>
              <p className="text-xs text-secondary-text">Reward</p>
              <p className="text-xl font-bold text-gaming-gold">{estimatedReward} JQ</p>
            </div>
            {combo > 0 && (
              <div className="text-center bg-gradient-to-r from-xp-purple/20 to-epic-purple/20 border border-xp-purple/30 rounded-xl p-3 min-w-[80px]">
                <div className="flex items-center justify-center mb-1">
                  <Zap className="w-4 h-4 text-xp-purple mr-1" />
                </div>
                <p className="text-xs text-secondary-text">Combo</p>
                <p className="text-xl font-bold text-xp-purple">x{combo}</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Enhanced Game Area */}
      <div className="relative z-10 flex h-[calc(100vh-180px)]">
        {/* Main Game View */}
        <div className="flex-1 relative">
          {/* Game Canvas */}
          <div className="h-full bg-gradient-to-br from-deep-space via-primary-blue/20 to-accent-cyan/10 flex items-center justify-center relative overflow-hidden">
            {/* Enhanced animated background elements */}
            <div className="absolute inset-0">
              {[...Array(30)].map((_, i) => (
                <div
                  key={i}
                  className={`absolute rounded-full animate-float ${
                    i % 3 === 0 ? 'bg-accent-cyan' : 
                    i % 3 === 1 ? 'bg-xp-purple' : 'bg-gaming-gold'
                  }`}
                  style={{
                    width: `${Math.random() * 8 + 4}px`,
                    height: `${Math.random() * 8 + 4}px`,
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`,
                    opacity: 0.3,
                    animationDelay: `${Math.random() * 5}s`,
                    animationDuration: `${4 + Math.random() * 4}s`,
                  }}
                />
              ))}
            </div>

            {/* Power-up indicators */}
            {powerUps > 0 && (
              <div className="absolute top-10 left-10 flex space-x-2">
                {[...Array(Math.min(powerUps, 5))].map((_, i) => (
                  <div key={i} className="w-8 h-8 bg-gradient-to-r from-legendary-orange to-gaming-gold rounded-full flex items-center justify-center animate-pulse">
                    <Star className="w-4 h-4 text-deep-space" />
                  </div>
                ))}
              </div>
            )}

            {/* Game UI Overlay */}
            <div className="relative z-10 text-center">
              <div className="text-8xl mb-8 animate-bounce" style={{ animationDuration: '2s' }}>
                {game.id === '1' ? '🏃‍♂️' : game.id === '2' ? '🧩' : '🏰'}
              </div>
              
              {!isPlaying && timeLeft > 0 && (
                <div className="space-y-6 bg-gradient-to-br from-card-dark/90 to-surface-dark/80 backdrop-blur-xl border border-border-light/30 rounded-2xl p-8">
                  <h2 className="text-4xl font-bold bg-gradient-to-r from-accent-cyan to-gaming-gold bg-clip-text text-transparent">
                    Ready to Dominate?
                  </h2>
                  <p className="text-secondary-text text-lg leading-relaxed max-w-md mx-auto">
                    Collect crypto tokens, avoid obstacles, and climb the leaderboard to earn epic rewards!
                  </p>
                  <div className="flex items-center justify-center gap-4 text-sm text-muted-text">
                    <div className="flex items-center gap-1">
                      <Crown className="w-4 h-4 text-gaming-gold" />
                      <span>Elite Gaming</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Award className="w-4 h-4 text-accent-cyan" />
                      <span>Instant Rewards</span>
                    </div>
                  </div>
                  <Button onClick={handleStartGame} size="lg" className="bg-gradient-to-r from-gaming-gold to-legendary-orange text-deep-space font-bold text-xl px-12 py-6 hover:scale-110 transition-all duration-300 shadow-2xl">
                    <Play className="mr-3 h-6 w-6" />
                    Start Epic Battle
                  </Button>
                </div>
              )}

              {isPlaying && (
                <div className="space-y-6">
                  <div className="bg-gradient-to-br from-card-dark/90 to-surface-dark/80 backdrop-blur-xl border border-border-light/30 rounded-2xl p-6">
                    <div className="text-6xl font-bold text-accent-cyan mb-4">{score.toLocaleString()}</div>
                    {combo > 1 && (
                      <div className="text-2xl font-bold text-xp-purple mb-2 animate-pulse">
                        COMBO x{combo}!
                      </div>
                    )}
                    <div className="mb-4">
                      <div className="flex justify-between text-sm text-secondary-text mb-2">
                        <span>Game Progress</span>
                        <span>{Math.round(gameProgress)}%</span>
                      </div>
                      <div className="w-80 mx-auto h-4 bg-surface-dark rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-gradient-to-r from-accent-cyan to-gaming-gold transition-all duration-300"
                          style={{ width: `${gameProgress}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                  <div className="flex space-x-4 justify-center">
                    <Button onClick={handlePauseGame} variant="outline" className="border-legendary-orange text-legendary-orange hover:bg-legendary-orange/10 px-6 py-3">
                      <Pause className="mr-2 h-4 w-4" />
                      Pause
                    </Button>
                    <Button onClick={handleRestart} variant="outline" className="border-health-red text-health-red hover:bg-health-red/10 px-6 py-3">
                      <RotateCcw className="mr-2 h-4 w-4" />
                      Restart
                    </Button>
                  </div>
                </div>
              )}

              {timeLeft === 0 && (
                <div className="space-y-6 bg-gradient-to-br from-card-dark/90 to-surface-dark/80 backdrop-blur-xl border border-border-light/30 rounded-2xl p-8">
                  <div className="text-8xl mb-4">🎉</div>
                  <h2 className="text-4xl font-bold bg-gradient-to-r from-gaming-gold to-legendary-orange bg-clip-text text-transparent">
                    Epic Victory!
                  </h2>
                  <div className="bg-gradient-to-r from-gaming-gold/20 to-legendary-orange/20 border border-gaming-gold/30 rounded-xl p-6">
                    <div className="text-3xl font-bold text-gaming-gold mb-2">
                      +{estimatedReward} JQ Earned!
                    </div>
                    <div className="text-secondary-text">Final Score: {score.toLocaleString()}</div>
                    <div className="text-sm text-accent-cyan mt-2">
                      Rank #{rankPosition} on Leaderboard!
                    </div>
                  </div>
                  <div className="flex space-x-4 justify-center">
                    <Button onClick={handleRestart} className="bg-gradient-to-r from-accent-cyan to-xp-purple text-white font-bold px-8 py-3 hover:scale-105 transition-all duration-300">
                      <RotateCcw className="mr-2 h-4 w-4" />
                      Battle Again
                    </Button>
                    <Button asChild variant="outline" className="border-accent-cyan text-accent-cyan hover:bg-accent-cyan/10 px-8 py-3">
                      <Link to="/games">Back to Hub</Link>
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Enhanced Sidebar */}
        <div className="w-80 bg-gradient-to-b from-card-dark/95 to-surface-dark/90 backdrop-blur-xl border-l border-border-light/30 p-6 space-y-6 overflow-y-auto">
          {/* Player Performance */}
          <div className="bg-gradient-to-br from-surface-dark/80 to-border-light/40 backdrop-blur-sm border border-border-light/30 rounded-xl p-4 hover:border-accent-cyan/30 transition-all duration-300">
            <h3 className="font-bold mb-4 flex items-center text-lg">
              <Trophy className="mr-2 h-5 w-5 text-gaming-gold" />
              Your Performance
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-secondary-text">Best Score</span>
                <span className="font-bold text-gaming-gold">{playerStats.bestScore.toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-secondary-text">Games Played</span>
                <span className="font-bold text-accent-cyan">{playerStats.gamesPlayed}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-secondary-text">Total Earned</span>
                <span className="font-bold text-success-green">{playerStats.totalEarned.toLocaleString()} JQ</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-secondary-text">Win Rate</span>
                <span className="font-bold text-xp-purple">{playerStats.winRate}%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-secondary-text">Player Level</span>
                <span className="font-bold text-legendary-orange">Level {playerStats.level}</span>
              </div>
            </div>
          </div>

          {/* Enhanced Live Leaderboard */}
          <div className="bg-gradient-to-br from-surface-dark/80 to-border-light/40 backdrop-blur-sm border border-border-light/30 rounded-xl p-4 hover:border-gaming-gold/30 transition-all duration-300">
            <h3 className="font-bold mb-4 flex items-center text-lg">
              <Crown className="mr-2 h-5 w-5 text-gaming-gold" />
              Hall of Legends
            </h3>
            <div className="space-y-3">
              {[
                { name: 'CryptoGamer123', score: 89420, avatar: '🏆' },
                { name: 'BlockchainPro', score: 76543, avatar: '⚡' },
                { name: 'NFTHunter', score: 65432, avatar: '🎯' },
                { name: 'You', score: score, avatar: '🚀' },
                { name: 'Web3Warrior', score: 45321, avatar: '💎' },
              ].sort((a, b) => b.score - a.score).slice(0, 5).map((player, index) => (
                <div
                  key={index}
                  className={`flex items-center justify-between p-3 rounded-xl transition-all duration-300 ${
                    player.name === 'You' 
                      ? 'bg-gradient-to-r from-accent-cyan/20 to-xp-purple/20 border border-accent-cyan/30' 
                      : 'bg-surface-dark/40 hover:bg-surface-dark/60'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`w-8 h-8 rounded-xl flex items-center justify-center font-bold text-sm ${
                      index === 0 ? 'bg-gradient-to-br from-gaming-gold to-legendary-orange text-deep-space' :
                      index === 1 ? 'bg-gradient-to-br from-secondary-text to-border-light text-deep-space' :
                      index === 2 ? 'bg-gradient-to-br from-legendary-orange to-gaming-gold text-deep-space' :
                      'bg-surface-dark border border-border-light text-secondary-text'
                    }`}>
                      {index < 3 ? player.avatar : index + 1}
                    </div>
                    <span className={`${player.name === 'You' ? 'font-bold text-accent-cyan' : 'text-primary-text'}`}>
                      {player.name}
                    </span>
                  </div>
                  <span className="font-bold text-gaming-gold">{player.score.toLocaleString()}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Enhanced Game Controls */}
          <div className="bg-gradient-to-br from-surface-dark/80 to-border-light/40 backdrop-blur-sm border border-border-light/30 rounded-xl p-4 hover:border-xp-purple/30 transition-all duration-300">
            <h3 className="font-bold mb-4 flex items-center text-lg">
              <Zap className="mr-2 h-5 w-5 text-xp-purple" />
              Game Controls
            </h3>
            <div className="space-y-3 text-sm">
              <div className="flex items-center justify-between p-2 bg-surface-dark/40 rounded-lg">
                <span className="text-secondary-text">Movement</span>
                <span className="font-mono text-accent-cyan">Arrow Keys</span>
              </div>
              <div className="flex items-center justify-between p-2 bg-surface-dark/40 rounded-lg">
                <span className="text-secondary-text">Action</span>
                <span className="font-mono text-accent-cyan">Spacebar</span>
              </div>
              <div className="flex items-center justify-between p-2 bg-surface-dark/40 rounded-lg">
                <span className="text-secondary-text">Pause</span>
                <span className="font-mono text-accent-cyan">P Key</span>
              </div>
              <div className="flex items-center justify-between p-2 bg-surface-dark/40 rounded-lg">
                <span className="text-secondary-text">Restart</span>
                <span className="font-mono text-accent-cyan">R Key</span>
              </div>
            </div>
          </div>

          {/* Game Tips */}
          <div className="bg-gradient-to-br from-surface-dark/80 to-border-light/40 backdrop-blur-sm border border-border-light/30 rounded-xl p-4 hover:border-success-green/30 transition-all duration-300">
            <h3 className="font-bold mb-4 flex items-center text-lg">
              <Star className="mr-2 h-5 w-5 text-success-green" />
              Pro Tips
            </h3>
            <div className="space-y-2 text-sm text-secondary-text">
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-gaming-gold rounded-full mt-2 flex-shrink-0"></div>
                <span>Collect combo multipliers for higher scores</span>
              </div>
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-accent-cyan rounded-full mt-2 flex-shrink-0"></div>
                <span>Power-ups boost your earning potential</span>
              </div>
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-xp-purple rounded-full mt-2 flex-shrink-0"></div>
                <span>Consistency beats high-risk plays</span>
              </div>
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-success-green rounded-full mt-2 flex-shrink-0"></div>
                <span>Practice daily to climb rankings</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GamePlay;